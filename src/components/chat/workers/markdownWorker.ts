/**
 * markdownWorker.ts
 * 该Web Worker用于在后台线程处理Markdown渲染，避免阻塞主UI线程
 */

// 导入所需的库
import { marked } from 'marked';

// 声明Worker上下文类型
declare const self: Worker;

// Worker消息数据接口
interface WorkerMessageData {
  markdown: string;  // 要处理的markdown内容
  id: string;        // 消息标识符
}

// Worker响应数据接口
interface WorkerResponseData {
  id: string;        // 消息标识符
  html?: string;     // 处理后的HTML
  codeBlocks?: CodeBlock[]; // 提取的代码块
  error?: string;    // 错误信息
  success: boolean;  // 是否成功
}

// 代码块接口
interface CodeBlock {
  language: string;  // 语言类型
  code: string;      // 代码内容
  fullMatch: string; // 原始匹配内容
}

/**
 * 配置marked选项
 */
const configureMarked = (): void => {
  marked.setOptions({
    gfm: true,          // GitHub风格Markdown
    breaks: true,       // 允许换行符转换为<br>
    pedantic: false,
    smartLists: true,
    smartypants: false,
    xhtml: false
  });
};

// 初始化marked配置
configureMarked();

/**
 * 从HTML中提取代码块
 * @param html 处理后的HTML内容
 * @returns 提取的代码块数组
 */
function extractCodeBlocks(html: string): CodeBlock[] {
  const codeBlockRegex = /<pre><code class="language-([a-zA-Z0-9]+)">([\s\S]*?)<\/code><\/pre>/g;
  const codeBlocks: CodeBlock[] = [];
  let match: RegExpExecArray | null;
  
  while ((match = codeBlockRegex.exec(html)) !== null) {
    codeBlocks.push({
      language: match[1],
      code: match[2].replace(/&lt;/g, '<').replace(/&gt;/g, '>').replace(/&amp;/g, '&'),
      fullMatch: match[0]
    });
  }
  
  return codeBlocks;
}

/**
 * 处理Markdown内容并转换为HTML
 * @param markdown 原始markdown文本
 * @returns 处理后的HTML（主线程应进行安全处理）
 */
function processMarkdown(markdown: string): string {
  // 确保marked.parse返回字符串而非Promise
  try {
    // 使用同步模式处理
    return marked.parse(markdown, { async: false }) as string;
  } catch (error) {
    console.error('解析Markdown时出错:', error);
    return `解析错误: ${error instanceof Error ? error.message : String(error)}`;
  }
}

// 处理来自主线程的消息
self.onmessage = function(e: MessageEvent<WorkerMessageData>): void {
  try {
    const { markdown, id } = e.data;
    
    if (!markdown) {
      self.postMessage({ 
        id, 
        error: '没有提供Markdown内容', 
        success: false 
      } as WorkerResponseData);
      return;
    }
    
    // 处理Markdown
    const htmlContent = processMarkdown(markdown);
    
    // 提取特殊代码块以供主线程处理语法高亮
    const codeBlocks = extractCodeBlocks(htmlContent);
    
    // 发送结果回主线程
    self.postMessage({
      id,
      html: htmlContent,
      codeBlocks: codeBlocks,
      success: true
    } as WorkerResponseData);
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    self.postMessage({
      id: e.data.id,
      error: errorMessage,
      success: false
    } as WorkerResponseData);
  }
};
