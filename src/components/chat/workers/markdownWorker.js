/**
 * markdownWorker.js
 * 该Web Worker用于在后台线程处理Markdown渲染，避免阻塞主UI线程
 */

// 导入marked库
import { marked } from 'marked';

/**
 * 配置marked选项
 */
const configureMarked = () => {
  marked.setOptions({
    gfm: true,          // GitHub风格Markdown
    breaks: true,       // 允许换行符转换为<br>
    pedantic: false,
    smartLists: true,
    smartypants: false,
    xhtml: false
  });
};

// 初始化marked配置
configureMarked();

/**
 * 从MKarkdown提取代码块
 * @param {string} html 处理后的HTML内容
 * @returns {Array} 提取的代码块数组
 */
function extractCodeBlocks(html) {
  const codeBlockRegex = /<pre><code class="language-([a-zA-Z0-9]+)">(([\s\S]*?))<\/code><\/pre>/g;
  const codeBlocks = [];
  let match;
  
  while ((match = codeBlockRegex.exec(html)) !== null) {
    codeBlocks.push({
      language: match[1],
      code: match[2].replace(/&lt;/g, '<').replace(/&gt;/g, '>').replace(/&amp;/g, '&'),
      fullMatch: match[0]
    });
  }
  
  return codeBlocks;
}

/**
 * 处理Markdown内容并转换为HTML
 * @param {string} markdown 原始markdown文本
 * @returns {string} 处理后的HTML（主线程应进行安全处理）
 */
function processMarkdown(markdown) {
  try {
    // 使用同步模式处理
    return marked.parse(markdown, { async: false });
  } catch (error) {
    console.error('解析Markdown时出错:', error);
    return `解析错误: ${error.message || String(error)}`;
  }
}

// 处理来自主线程的消息
self.onmessage = function(e) {
  try {
    const { markdown, id } = e.data;
    
    if (!markdown) {
      self.postMessage({ 
        id, 
        error: '没有提供Markdown内容', 
        success: false 
      });
      return;
    }
    
    // 处理Markdown
    const htmlContent = processMarkdown(markdown);
    
    // 提取特殊代码块以供主线程处理语法高亮
    const codeBlocks = extractCodeBlocks(htmlContent);
    
    // 发送结果回主线程
    self.postMessage({
      id,
      html: htmlContent,
      codeBlocks: codeBlocks,
      success: true
    });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    self.postMessage({
      id: e.data.id,
      error: errorMessage,
      success: false
    });
  }
};

// 已替换为使用marked库的实现
