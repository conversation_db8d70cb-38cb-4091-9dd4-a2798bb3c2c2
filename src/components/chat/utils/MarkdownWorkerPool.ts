/**
 * Markdown Worker池管理工具
 * 用于管理多个Worker实例，提高Markdown处理性能
 */

// 定义任务类型
interface MarkdownTask {
  id: string;
  markdown: string;
  resolve: (result: WorkerResponseData) => void;
  reject: (error: Error) => void;
}

// 定义Worker消息数据类型
interface WorkerMessageData {
  markdown: string;
  id: string;
}

// 定义Worker响应数据类型
interface WorkerResponseData {
  id: string;
  html?: string;
  error?: string;
  success: boolean;
}

// 定义取消错误类型
interface CancellationError extends Error {
  isCancellation: boolean;
}

/**
 * Markdown Worker池
 * 管理多个Worker实例，根据负载分配任务
 */
export class MarkdownWorkerPool {
  private workers: Worker[] = [];
  private taskQueue: MarkdownTask[] = [];
  private activeWorkers: Map<Worker, string> = new Map();
  private isInitialized: boolean = false;
  private taskIdCounter: number = 0;
  
  /**
   * 创建一个Markdown Worker池
   * @param poolSize Worker实例数量，默认为当前环境支持的逻辑处理器数量或2
   */
  constructor(private poolSize: number = Math.max(2, navigator.hardwareConcurrency || 2)) {
    // 注意：poolSize会在initialize方法中使用
    console.log(`[Markdown Worker池] 初始化Worker池，大小: ${this.poolSize}`);
  }
  
  /**
   * 初始化Worker池
   * 懒加载方式，仅在首次使用时创建Worker
   */
  private initialize(): void {
    if (this.isInitialized) return;
    
    console.log(`[Markdown Worker池] 开始创建${this.poolSize}个Worker实例`);
    for (let i = 0; i < this.poolSize; i++) {
      this.createWorker();
    }
    
    this.isInitialized = true;
  }
  
  /**
   * 创建单个Worker实例
   * @returns 创建的Worker实例
   */
  private createWorker(): Worker {
    try {
      // 先尝试加载TypeScript版本
      let worker: Worker | null = null;
      
      try {
        const workerURL = new URL('../workers/markdownWorker.ts', import.meta.url).href;
        worker = new Worker(workerURL, { type: 'module' });
      } catch (error) {
        console.warn('[Markdown Worker池] 加载TypeScript Worker失败，尝试JavaScript版本:', error);
        
        // 如果失败，尝试加载JavaScript版本
        const workerURL = new URL('../workers/markdownWorker.js', import.meta.url).href;
        worker = new Worker(workerURL);
      }
      
      if (!worker) {
        throw new Error('无法创建Worker实例');
      }
      
      // 设置消息处理
      worker.onmessage = this.handleWorkerMessage.bind(this, worker);
      worker.onerror = this.handleWorkerError.bind(this, worker);
      
      this.workers.push(worker);
      console.log(`[Markdown Worker池] Worker #${this.workers.length} 创建成功`);
      return worker;
    } catch (error) {
      console.error('[Markdown Worker池] 创建Worker失败:', error);
      throw error;
    }
  }
  
  /**
   * 处理Worker发送的消息
   * @param worker 发送消息的Worker实例
   * @param e 消息事件
   */
  private handleWorkerMessage(worker: Worker, e: MessageEvent<WorkerResponseData>): void {
    const { id, success, error } = e.data;
    const taskId = this.activeWorkers.get(worker);
    
    if (taskId !== id) {
      console.warn(`[Markdown Worker池] 收到意外的消息ID: ${id}, 期望的ID: ${taskId}`);
    }
    
    // 释放Worker
    this.activeWorkers.delete(worker);
    console.log(`[Markdown Worker池] Worker已完成任务 ${id}, 当前活跃Worker: ${this.activeWorkers.size}/${this.workers.length}`);
    
    // 查找对应的任务
    const pendingTaskIndex = this.taskQueue.findIndex(task => task.id === id);
    if (pendingTaskIndex !== -1) {
      const pendingTask = this.taskQueue[pendingTaskIndex];
      
      // 从队列中移除任务
      this.taskQueue.splice(pendingTaskIndex, 1);
      
      // 处理结果
      if (success) {
        pendingTask.resolve(e.data);
      } else {
        pendingTask.reject(new Error(error || '未知错误'));
      }
    }
    
    // 处理下一个任务
    this.processNextTask();
  }
  
  /**
   * 处理Worker错误
   * @param worker 发生错误的Worker实例
   * @param e 错误事件
   */
  private handleWorkerError(worker: Worker, e: ErrorEvent): void {
    const taskId = this.activeWorkers.get(worker);
    console.error(`[Markdown Worker池] Worker错误 (任务 ${taskId}):`, e.message);
    
    // 从活跃列表中移除
    this.activeWorkers.delete(worker);
    
    // 从Worker列表中移除
    const index = this.workers.findIndex(w => w === worker);
    if (index !== -1) {
      this.workers.splice(index, 1);
    }
    
    // 查找对应的任务
    if (taskId) {
      const pendingTaskIndex = this.taskQueue.findIndex(task => task.id === taskId);
      if (pendingTaskIndex !== -1) {
        const pendingTask = this.taskQueue[pendingTaskIndex];
        
        // 从队列中移除任务
        this.taskQueue.splice(pendingTaskIndex, 1);
        
        // 通知任务失败
        pendingTask.reject(new Error(`Worker错误: ${e.message}`));
      }
    }
    
    // 创建新Worker替换失败的Worker
    try {
      this.createWorker();
    } catch (err) {
      console.error('[Markdown Worker池] 无法创建替代Worker:', err);
    }
    
    // 处理下一个任务
    this.processNextTask();
  }
  
  /**
   * 处理Markdown内容
   * @param markdown 要处理的Markdown文本
   * @param customId 可选的自定义任务ID
   * @returns Promise，包含处理结果
   */
  processMarkdown(markdown: string, customId?: string): Promise<WorkerResponseData> {
    // 确保Worker池已初始化
    if (!this.isInitialized) {
      this.initialize();
    }
    
    // 生成唯一任务ID
    const id = customId || `task_${Date.now()}_${this.taskIdCounter++}`;
    
    return new Promise((resolve, reject) => {
      // 将任务添加到队列
      this.taskQueue.push({ markdown, id, resolve, reject });
      console.log(`[Markdown Worker池] 添加任务 ${id}，当前队列长度: ${this.taskQueue.length}`);
      
      // 尝试立即处理任务
      this.processNextTask();
    });
  }
  
  /**
   * 处理队列中的下一个任务
   */
  private processNextTask(): void {
    if (this.taskQueue.length === 0) {
      console.log('[Markdown Worker池] 无待处理任务');
      return;
    }
    
    // 查找空闲的Worker
    const availableWorker = this.workers.find(
      worker => !this.activeWorkers.has(worker)
    );
    
    if (!availableWorker) {
      console.log('[Markdown Worker池] 无可用Worker，等待中...');
      return; // 没有空闲Worker
    }
    
    // 获取下一个任务
    const nextTask = this.taskQueue[0];
    
    // 标记任务为处理中（但不从队列中移除，直到完成）
    this.activeWorkers.set(availableWorker, nextTask.id);
    console.log(`[Markdown Worker池] 分配任务 ${nextTask.id} 给Worker，当前活跃Worker: ${this.activeWorkers.size}/${this.workers.length}`);
    
    // 发送任务到Worker
    availableWorker.postMessage({
      markdown: nextTask.markdown,
      id: nextTask.id
    } as WorkerMessageData);
  }
  
  /**
   * 获取当前队列中的任务数量
   * @returns 任务队列长度
   */
  get queueLength(): number {
    return this.taskQueue.length;
  }
  
  /**
   * 获取活跃Worker数量
   * @returns 活跃Worker数
   */
  get activeWorkerCount(): number {
    return this.activeWorkers.size;
  }
  
  /**
   * 获取总工作者数
   * @returns 工作者总数
   */
  get totalWorkerCount(): number {
    return this.workers.length;
  }
  
  // 创建取消错误对象
  private createCancellationError(): CancellationError {
    const error = new Error('任务已取消') as CancellationError;
    error.isCancellation = true;
    return error;
  }
  
  /**
   * 取消任务
   * @param taskId 要取消的任务ID
   * @returns 如果成功取消任务返回true，否则返回false
   */
  cancelTask(taskId: string): boolean {
    console.log(`[Markdown Worker池] 尝试取消任务: ${taskId}`);
    
    // 检查是否在队列中
    const queueIndex = this.taskQueue.findIndex(task => task.id === taskId);
    
    if (queueIndex !== -1) {
      // 从队列中移除任务
      const task = this.taskQueue[queueIndex];
      this.taskQueue.splice(queueIndex, 1);
      
      // 通知任务已取消
      task.reject(this.createCancellationError());
      console.log(`[Markdown Worker池] 成功从队列中取消任务: ${taskId}`);
      return true;
    }
    
    // 检查是否正在处理
    for (const [worker, activeTaskId] of this.activeWorkers.entries()) {
      if (activeTaskId === taskId) {
        // 此任务正在处理中
        console.log(`[Markdown Worker池] 发现正在处理的任务: ${taskId}, 尝试中断...`);
        
        try {
          // 终止Worker并创建新Worker
          worker.terminate();
          this.activeWorkers.delete(worker);
          
          // 从数组中移除该Worker
          const workerIndex = this.workers.findIndex(w => w === worker);
          if (workerIndex !== -1) {
            this.workers.splice(workerIndex, 1);
          }
          
          // 搜索相关任务并通知取消
          const taskIndex = this.taskQueue.findIndex(task => task.id === taskId);
          if (taskIndex !== -1) {
            const task = this.taskQueue[taskIndex];
            this.taskQueue.splice(taskIndex, 1);
            task.reject(this.createCancellationError());
          }
          
          // 创建新Worker替代
          this.createWorker();
          
          console.log(`[Markdown Worker池] 成功中断正在处理的任务: ${taskId}`);
          return true;
        } catch (error) {
          console.error(`[Markdown Worker池] 取消任务失败: ${error}`);
          return false;
        }
      }
    }
    
    // 未找到对应任务
    console.log(`[Markdown Worker池] 未找到任务: ${taskId}`);
    return false;
  }
  
  /**
   * 终止所有Worker并清理资源
   */
  terminate(): void {
    console.log('[Markdown Worker池] 终止所有Worker实例');
    
    // 终止所有Worker
    this.workers.forEach(worker => {
      try {
        worker.terminate();
      } catch (error) {
        console.error('[Markdown Worker池] 终止Worker时出错:', error);
      }
    });
    
    // 重置状态
    this.workers = [];
    this.activeWorkers.clear();
    
    // 拒绝所有待处理任务
    this.taskQueue.forEach(task => {
      task.reject(new Error('Worker池已终止'));
    });
    this.taskQueue = [];
    
    this.isInitialized = false;
  }
}

// 创建单例实例
export const markdownWorkerPool = new MarkdownWorkerPool();

// 导出单例实例作为默认导出
export default markdownWorkerPool;
