import { Bubble } from '@ant-design/x';
import React from 'react';
import { Message } from '../types';
import { WelcomeScreen } from './index';
// 导入Markdown样式
import './MessageList.css';
// 导入独立的RenderMarkdown组件
import RenderMarkdown from './RenderMarkdown';
// 导入视口渲染组件
import LazyRender from './LazyRender';

/**
 * 处理并合并连续的非用户消息
 * @param originalMessages 原始消息数组
 * @returns 处理后的消息数组，连续的assistant消息被合并
 */
const processMergedMessages = (originalMessages: Message[]) => {
  // 判断消息数量是否小于阈值，决定是否使用惰性加载
  // 当消息数量小于 50 条时，直接渲染所有内容，不使用 LazyRender
  const shouldUseDirectRender = originalMessages.length < 50;
  const result: Array<{
    key: string;
    role: 'local' | 'ai';
    content: React.ReactNode | string;
  }> = [];

  let currentAssistantMessages: Message[] = [];

  // 遍历所有消息，将连续的assistant消息合并
  originalMessages.forEach((msg, index) => {
    if (msg.type === 'assistant') {
      // 收集连续的assistant消息
      currentAssistantMessages.push(msg);

      // 如果是最后一条消息或下一条是用户消息，则合并当前收集的assistant消息
      if (index === originalMessages.length - 1 || originalMessages[index + 1].type === 'user') {
        if (currentAssistantMessages.length > 0) {
          // 合并多条assistant消息
          const mergedContent = currentAssistantMessages.map((assistantMsg, msgIdx) => {
            // 检查是否是最后一条消息（可能正在流式更新）
            const isLastMessage = index === originalMessages.length - 1 && 
                              msgIdx === currentAssistantMessages.length - 1;
            
            // 设置唯一标识符，方便调试和跟踪
            const contentId = `msg-${assistantMsg.id}-${msgIdx}`;
            
            // 如果是最后一条消息或消息总数小于 50条，直接渲染不使用LazyRender
            // 这确保流式更新可见，并且小对话更加高效
            if (isLastMessage || shouldUseDirectRender) {
              return (
                <div key={contentId} className="markdown-content-wrapper">
                  <RenderMarkdown content={assistantMsg.message} />
                </div>
              );
            }
            
            // 当消息数量较多时，非最后一条消息使用LazyRender提升性能
            return (
              <LazyRender key={contentId} placeholder={<div className="markdown-placeholder">加载中...</div>}>
                <RenderMarkdown content={assistantMsg.message} />
              </LazyRender>
            );
          });

          result.push({
            key: currentAssistantMessages.map(m => m.id).join('-'),
            role: 'ai',
            content: <div className="merged-assistant-messages">{mergedContent}</div>
          });

          // 重置收集器
          currentAssistantMessages = [];
        }
      }
    } else {
      // 处理用户消息前，确保之前收集的assistant消息已处理
      if (currentAssistantMessages.length > 0) {
        const mergedContent = currentAssistantMessages.map((assistantMsg, msgIdx) => {
          // 设置唯一的内容ID
          const contentId = `msg-${assistantMsg.id}-${msgIdx}`;
          
          // 检查是否是最后一条消息
          const isNewestMessage = msgIdx === currentAssistantMessages.length - 1;
          
          // 如果是最后一条消息或消息总数小于 50 条，直接渲染
          if (isNewestMessage || shouldUseDirectRender) {
            return (
              <div key={contentId} className="markdown-content-wrapper">
                <RenderMarkdown content={assistantMsg.message} />
              </div>
            );
          }
          
          // 当消息数量较多时，非最后消息才使用 LazyRender 提升性能
          return (
            <LazyRender key={contentId} placeholder={<div className="markdown-placeholder">加载中...</div>}>
              <RenderMarkdown content={assistantMsg.message} />
            </LazyRender>
          );
        });

        result.push({
          key: currentAssistantMessages.map(m => m.id).join('-'),
          role: 'ai',
          content: <div className="merged-assistant-messages">{mergedContent}</div>
        });

        // 重置收集器
        currentAssistantMessages = [];
      }

      // 添加用户消息
      result.push({
        key: msg.id,
        role: 'local',
        content: msg.message
      });
    }
  });

  return result;
};

/**
 * 消息列表组件
 * 负责渲染聊天消息或欢迎屏幕
 * 支持Markdown格式化显示
 * 支持连续的非用户消息合并展示
 */
const MessageList: React.FC<{ messages: Message[] }> = React.memo(({ messages }) => {
  if (messages.length === 0) {
    return <WelcomeScreen />;
  }

  // 使用外部处理函数处理消息
  const processedItems = processMergedMessages(messages);

  return (
    <Bubble.List
      items={processedItems}
      roles={{
        ai: {
          placement: 'start',
          typing: { step: 5, interval: 20 },
          styles: {
            content: {
              borderRadius: '16px',
              backgroundColor: 'transparent', // AI 气泡背景设置为透明
            }
          },
          // 添加所需的自定义样式
          className: 'markdown-content',
        },
        local: {
          placement: 'end',
          variant: 'shadow',
          styles: {
            content: {
              backgroundColor: '#E9E9EB', // 用户气泡的新背景色
            }
          },
        },
      }}
    />
  );
});

export default MessageList;
