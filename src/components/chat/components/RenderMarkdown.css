/* Markdown 表格样式 */
.markdown-table-container {
  width: 100%;
  overflow-x: auto;
  margin: 16px 0;
}

.markdown-table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  font-size: 14px;
}

.markdown-table-head {
  background-color: #f5f5f5;
}

.markdown-table-header {
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  border: 1px solid #e8e8e8;
}

.markdown-table-row:hover {
  background-color: #f9f9f9;
}

.markdown-table-cell {
  padding: 12px 16px;
  border: 1px solid #e8e8e8;
}

/* 确保表格在移动设备上也能正常显示 */
@media (max-width: 768px) {
  .markdown-table-container {
    width: 100%;
    overflow-x: auto;
  }
  
  .markdown-table {
    min-width: 600px;
  }
}
