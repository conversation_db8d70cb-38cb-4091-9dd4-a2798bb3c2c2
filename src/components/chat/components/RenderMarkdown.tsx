import DOMPurify from 'dompurify';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import { markdownWorkerPool } from '../utils/MarkdownWorkerPool';
import { isWorkerSupported } from '../utils/workerHelper';

// 条件开关函数：决定是否使用 Web Worker 渲染 Markdown
// 可以在这个函数中添加各种条件判断逻辑
type ShouldUseWorkerFn = (content: string) => boolean;

// 默认的判断函数实现
const defaultShouldUseWorker: ShouldUseWorkerFn = (content: string) => {
  // 默认情况下，满足以下条件时使用 Worker：
  // 1. 浏览器支持 Web Worker
  // 2. 内容长度超过一定阈值（这里设为 1000 字符）
  return isWorkerSupported() && content.length > 1000;
};

// 取消错误的接口定义
interface CancellationError extends Error {
  isCancellation: boolean;
}

// Worker响应数据类型
interface WorkerResponseData {
  id: string;
  html?: string;
  error?: string;
  success: boolean;
}

// 诊断：暂时改回直接导入语法高亮组件和样式
import SyntaxHighlighter from 'react-syntax-highlighter/dist/esm/prism-light';
import oneLight from 'react-syntax-highlighter/dist/esm/styles/prism/one-light';

// 导入并注册语言以支持语法高亮
import shell from 'react-syntax-highlighter/dist/esm/languages/prism/bash'; // bash 通常用于 shell 脚本
import css from 'react-syntax-highlighter/dist/esm/languages/prism/css';
import javascript from 'react-syntax-highlighter/dist/esm/languages/prism/javascript';
import json from 'react-syntax-highlighter/dist/esm/languages/prism/json';
import jsx from 'react-syntax-highlighter/dist/esm/languages/prism/jsx';
import python from 'react-syntax-highlighter/dist/esm/languages/prism/python';
import tsx from 'react-syntax-highlighter/dist/esm/languages/prism/tsx';
import typescript from 'react-syntax-highlighter/dist/esm/languages/prism/typescript';

SyntaxHighlighter.registerLanguage('javascript', javascript);
SyntaxHighlighter.registerLanguage('js', javascript);
SyntaxHighlighter.registerLanguage('typescript', typescript);
SyntaxHighlighter.registerLanguage('ts', typescript);
SyntaxHighlighter.registerLanguage('python', python);
SyntaxHighlighter.registerLanguage('py', python);
SyntaxHighlighter.registerLanguage('jsx', jsx);
SyntaxHighlighter.registerLanguage('tsx', tsx);
SyntaxHighlighter.registerLanguage('json', json);
SyntaxHighlighter.registerLanguage('css', css);
SyntaxHighlighter.registerLanguage('shell', shell);
SyntaxHighlighter.registerLanguage('bash', shell);

// 导入表格样式
import './RenderMarkdown.css';

// 定义用于检测 GFM 特性的正则表达式常量
// 这些常量可以在整个组件中复用，并且使代码更加清晰

// 表格模式：查找表格语法 | 和 -
const TABLE_PATTERN = /\|.*\|[\r\n]+(\|\s*:?-+:?\s*)+\|/;

// 任务列表模式: - [ ] 或 - [x]
// const TASK_LIST_PATTERN = /- \[[x\s]?\]/;

// 自动链接模式：不带方括号的网址 URL
// const AUTO_LINK_PATTERN = /\bwww\.\S+|https?:\/\/\S+/;

// 删除线模式：~~text~~
// const STRIKETHROUGH_PATTERN = /~~.+?~~/;

/**
 * 代码块渲染组件，支持语法高亮
 */
// 使用 React.ComponentPropsWithoutRef<'code'> 确保类型兼容性
const CodeBlock: React.FC<React.ComponentPropsWithoutRef<'code'> & {
  inline?: boolean;
  className?: string;
  children?: React.ReactNode;
}> = ({ inline, className, children, ...props }) => {
  const match = /language-(\w+)/.exec(className || '');
  return !inline && match ? (
    <SyntaxHighlighter
      // @ts-expect-error - 忽略style属性的类型检查，因为oneLight是有效的样式
      style={oneLight} // 主题风格
      language={match[1]}
      showLineNumbers={true}
      PreTag="div"
      customStyle={{
        maxHeight: '300px',
        overflowY: 'auto',
        borderRadius: '8px'
      }}
      {...props}
    >
      {String(children).replace(/\n$/, '')}
    </SyntaxHighlighter>
  ) : (
    <code className={className} {...props}>
      {children}
    </code>
  );
};


/**
 * 其他表格相关组件 - 使用React.memo优化
 */
const TableHead = React.memo((props: React.HTMLProps<HTMLTableSectionElement>) => (
  <thead className="markdown-table-head" {...props} />
));
TableHead.displayName = 'TableHead';

const TableBody = React.memo((props: React.HTMLProps<HTMLTableSectionElement>) => (
  <tbody className="markdown-table-body" {...props} />
));
TableBody.displayName = 'TableBody';

const TableRow = React.memo((props: React.HTMLProps<HTMLTableRowElement>) => (
  <tr className="markdown-table-row" {...props} />
));
TableRow.displayName = 'TableRow';

const TableHeader = React.memo((props: React.HTMLProps<HTMLTableHeaderCellElement>) => (
  <th className="markdown-table-header" {...props} />
));
TableHeader.displayName = 'TableHeader';

const TableCell = React.memo((props: React.HTMLProps<HTMLTableDataCellElement>) => (
  <td className="markdown-table-cell" {...props} />
));
TableCell.displayName = 'TableCell';

/**
 * 优化的Markdown渲染组件
 * 
 * 性能优化：
 * 1. 使用React.memo防止不必要的重渲染
 * 2. 使用useMemo缓存组件配置
 * 3. 实现语法高亮组件的懒加载
 * 4. 对大型内容使用分块处理
 * 5. 缓存已渲染的代码块
 */
export interface RenderMarkdownProps {
  content: string;
  className?: string;
  onProcessed?: (content: string, success: boolean) => void; // 添加处理完成回调
  shouldUseWorker?: ShouldUseWorkerFn; // 添加条件开关函数，决定是否使用 Web Worker
}

/**
 * Markdown渲染组件
 */
export const RenderMarkdown: React.FC<RenderMarkdownProps> = React.memo(({ 
  content, 
  className, 
  onProcessed,
  shouldUseWorker = defaultShouldUseWorker // 默认使用默认判断函数
}) => {
  // 状态变量
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [processedHTML, setProcessedHTML] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  // 记录markdown内容的引用，用于避免重复处理相同内容
  const contentRef = useRef<string>('');
  // 记录当前任务ID
  const taskIdRef = useRef<string>('');

  // 使用useMemo缓存组件配置，避免每次渲染都创建新的组件配置
  const components = useMemo(() => {
    // 在 useMemo 内部定义自定义表格组件，解决依赖问题
    // 使用更具体的标签类型，避免使用 any
    const TableWrapper = ({ children, ...props }: React.ComponentPropsWithoutRef<"table">) => (
      <table className="markdown-table" {...props}>{children}</table>
    );
    
    return {
      code: CodeBlock,
      table: TableWrapper,
      thead: TableHead,
      tbody: TableBody,
      tr: TableRow,
      th: TableHeader,
      td: TableCell
    };
  }, []);

  // 使用useMemo缓存ReactMarkdown配置和plugins
  // 使用 useMemo 根据内容是否需要 GFM 功能来条件加载 remarkGfm 插件
  const remarkPlugins = useMemo(() => {
    if (!content) return [];
    
    // 使用定义好的正则表达式常量检测内容是否需要 GFM 功能
    // 检查表格
    const hasTable = TABLE_PATTERN.test(content);
    
    // 检查任务列表
    //const hasTaskList = TASK_LIST_PATTERN.test(content);
    
    // 检查自动链接
    //const hasAutoLink = AUTO_LINK_PATTERN.test(content);
    
    // 检查删除线
    //const hasStrikethrough = STRIKETHROUGH_PATTERN.test(content);
    
    // 如果检测到任一 GFM 功能，加载 remarkGfm 插件
    if (hasTable) {
      console.log('[Markdown] 检测到需要 GFM 功能，加载 remarkGfm 插件');
      return [remarkGfm];
    }
    
    return [];
  }, [content]);

  // 添加rehypeRaw插件，支持渲染HTML标签
  const rehypePlugins = useMemo(() => [rehypeRaw], []);

  // 检查浏览器Worker支持情况
  useEffect(() => {
    if (!isWorkerSupported()) {
      setIsLoading(false);
      setError('浏览器不支持Web Workers，渲染可能会较慢');
    }
  }, []);

  // 当内容变化时，根据条件决定是否使用Worker处理
  useEffect(() => {
    // 如果内容为空或未变化，无需处理
    if (!content || content === contentRef.current) {
      return;
    }
    
    // 使用条件开关函数决定是否使用 Worker
    const useWorker = shouldUseWorker(content);
    // 如果不使用Worker，则直接跳过，等待同步渲染
    if (!useWorker) {
      setIsLoading(false);
      return;
    }
    
    // 更新内容引用
    contentRef.current = content;
    
    // 设置加载状态
    setIsLoading(true);
    setError(null);
    
    // 生成唯一的任务ID
    const taskId = `markdown_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    taskIdRef.current = taskId;
    
    // 设置超时保护
    const timeoutId = setTimeout(() => {
      setIsLoading(false);
      setError('处理超时，请尝试简化内容');
      
      // 超时时取消任务
      markdownWorkerPool.cancelTask(taskId);
      
      if (onProcessed) {
        onProcessed(content, false);
      }
    }, 10000); // 10秒超时
    
    // 使用Worker池处理Markdown
    markdownWorkerPool.processMarkdown(content, taskId)
      .then((result: WorkerResponseData) => {
        // 清除超时器
        clearTimeout(timeoutId);
        // 处理结果
        if (result.success && result.html) {
          // 在主线程中使用DOMPurify进行安全处理
          const sanitizedHTML = DOMPurify.sanitize(result.html);
          setProcessedHTML(sanitizedHTML);
          setError(null);
          setIsLoading(false);
          
          // 处理成功时调用回调
          if (onProcessed) {
            onProcessed(content, true);
          }
        } else if (result.error) {
          setError(`Markdown处理错误: ${result.error}`);
          setIsLoading(false);
          
          // 处理失败时也调用回调
          if (onProcessed) {
            onProcessed(content, false);
          }
        }
      })
      .catch(err => {
        // 清除超时器
        clearTimeout(timeoutId);
        
        // 判断是否为任务取消错误
        const isCancellationError = (err as CancellationError).isCancellation === true;
        
        if (!isCancellationError) {
          // 非取消错误才记录和显示
          console.error('[Markdown] Worker池错误:', err);
          setError(`处理错误: ${err.message || '未知错误'}`);
        } else {
          console.log('[Markdown] 任务已取消');
        }
        
        setIsLoading(false);
        
        // Worker错误时调用回调（任务取消也调用但不显示错误・
        if (onProcessed) {
          onProcessed(content, isCancellationError);
        }
      });
      
    // 清理函数
    return () => {
      clearTimeout(timeoutId);
      
      // 如果此时任务还在进行，则取消任务
      if (taskIdRef.current === taskId) {
        console.log(`[组件卸载] 取消Markdown任务: ${taskId}`);
        markdownWorkerPool.cancelTask(taskId);
      }
    };
  }, [content, onProcessed, shouldUseWorker]);
  
  // 确保在HTML处理完成后重置加载状态
  useEffect(() => {
    if (processedHTML) {
      setIsLoading(false);
      // 调用处理完成回调
      if (onProcessed) {
        onProcessed(content, true);
      }
    }
  }, [processedHTML, content, onProcessed]);
  
  // 判断是否使用了 Web Worker 进行渲染
  const workerEnabled = shouldUseWorker(content);
  
  return (
    <div className={`markdown-output-wrapper ${className || ''}`}>
      {/* 在使用 Worker 且内容较多时才显示加载状态 */}
      {isLoading && workerEnabled && content.length > 1000 && (
        <div className="markdown-loading">Markdown内容处理中...</div>
      )}
      
      {/* 错误提示 */}
      {error && (
        <div className="markdown-error">{error}</div>
      )}
      
      {/* 如果有已处理好的 HTML，则显示处理后的 HTML */}
      {processedHTML ? (
        <div dangerouslySetInnerHTML={{ __html: processedHTML }} />
      ) : (
        // 直接使用 ReactMarkdown 同步渲染的情况：
        // 1. Worker 处理失败时的备选方案
        // 2. 当条件开关不使用 Worker 时
        <ReactMarkdown
          remarkPlugins={remarkPlugins}
          rehypePlugins={rehypePlugins}
          components={components}
        >
          {content}
        </ReactMarkdown>
      )}
    </div>
  );
});

export default RenderMarkdown;
