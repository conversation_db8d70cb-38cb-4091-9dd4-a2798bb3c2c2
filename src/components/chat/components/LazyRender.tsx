import React, { useEffect, useRef, useState, useCallback } from 'react';
// 导入缓存工具
 import { hasRendered, addToCache } from '../utils/lazyRenderCache';

/**
 * 懒加载渲染组件
 * 只有当组件进入视口时才渲染实际内容
 * 已渲染过的内容会被缓存，避免重复渲染
 */
interface LazyRenderProps {
  children: React.ReactNode;
  placeholder?: React.ReactNode;
  rootMargin?: string;
  contentId?: string; // 用于标识内容的唯一ID
}

/**
 * 懒加载渲染组件
 * @param props 组件属性
 */
const LazyRender: React.FC<LazyRenderProps> = (props) => {
  const { 
    children, 
    placeholder = <div className="lazy-placeholder">加载中...</div>,
    rootMargin = '100px 0px', // 预加载区域，提前100px开始加载
    contentId // 可选的内容唯一标识符
  } = props;
  
  // 使用传入的contentId或生成一个基于children的唯一键
  const cacheKey = contentId || (typeof children === 'string' ? children : undefined);
  
  // 检查内容是否已经在缓存中
  const [isVisible, setIsVisible] = useState(false);
  const [isRendered, setIsRendered] = useState(cacheKey ? hasRendered(cacheKey) : false);
  const contentRef = useRef<React.ReactNode>(null);
  const ref = useRef<HTMLDivElement>(null);

  // 缓存渲染后的内容
  const cacheContent = useCallback(() => {
    if (cacheKey) {
      addToCache(cacheKey);
    }
    
    // 缓存实际渲染的内容引用
    if (!contentRef.current) {
      contentRef.current = children;
    }
  }, [cacheKey, children]);
  
  useEffect(() => {
    // 如果已经在缓存中，不需要进行观察
    if (isRendered) {
      cacheContent();
      return;
    }
    
    const currentRef = ref.current;
    if (!currentRef) return;

    // 创建观察器实例
    const observer = new IntersectionObserver(
      (entries) => {
        // 当组件进入视口时
        if (entries[0].isIntersecting) {
          setIsVisible(true);
          setIsRendered(true);
          cacheContent(); // 缓存内容
          // 一旦渲染后，不再需要观察
          observer.unobserve(currentRef);
        }
      },
      {
        rootMargin, // 设置预加载区域
        threshold: 0.01, // 只需要有一点点可见就开始渲染
      }
    );

    // 开始观察元素
    observer.observe(currentRef);

    // 组件卸载时清理观察器
    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [rootMargin, isRendered, cacheContent]);

  return (
    <div ref={ref} style={{ minHeight: '30px' }}>
      {isRendered ? (contentRef.current || children) : isVisible ? children : placeholder}
    </div>
  );
};

// 使用React.memo包装组件以避免不必要的重新渲染
const MemoizedLazyRender = React.memo(LazyRender);

export default MemoizedLazyRender;
